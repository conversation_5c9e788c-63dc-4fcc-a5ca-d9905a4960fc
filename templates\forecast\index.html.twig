{% extends 'base.html.twig' %}

{% block title %}Prévisions et tendances{% endblock %}

{% block stylesheets %}
{{ parent() }}
<style>
    .forecast-header {
        background-color: #f0f4f8;
        border-radius: 4px;
        padding: 15px 20px;
        margin-bottom: 20px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        border-left: 4px solid #0275d8;
    }

    .forecast-card {
        background-color: #fff;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        transition: all 0.2s ease;
        border: 1px solid #e9ecef;
    }

    .forecast-card:hover {
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        border-color: #dee2e6;
    }

    .card-header {
        background-color: #f8f9fa;
        color: #495057;
        padding: 12px 15px;
        border-radius: 4px 4px 0 0;
        font-weight: 600;
        border-bottom: 1px solid #e9ecef;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .card-body {
        padding: 15px;
    }

    .chart-container {
        position: relative;
        height: 300px;
        margin-top: 10px;
    }

    .stats-number {
        font-size: 2rem;
        font-weight: 600;
        color: #0275d8;
    }

    .stats-label {
        font-size: 0.85rem;
        color: #6c757d;
        font-weight: 500;
    }

    .prediction-card {
        border-left: 4px solid;
        transition: all 0.2s ease;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        margin-bottom: 10px;
        background-color: #fff;
        border: 1px solid #e9ecef;
    }

    .prediction-card:hover {
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        border-color: #dee2e6;
    }

    .prediction-card.up {
        border-left-color: #dc3545;
    }

    .prediction-card.down {
        border-left-color: #28a745;
    }

    .prediction-card.stable {
        border-left-color: #ffc107;
    }

    .prediction-card.info {
        border-left-color: #0dcaf0;
    }

    .prediction-icon {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 0;
        flex-shrink: 0;
        color: white;
        font-size: 0.9rem;
    }

    .prediction-icon.up {
        background-color: #dc3545;
    }

    .prediction-icon.down {
        background-color: #28a745;
    }

    .prediction-icon.stable {
        background-color: #ffc107;
    }

    .prediction-icon.info {
        background-color: #0dcaf0;
    }

    .document-list {
        max-height: 350px;
        overflow-y: auto;
        margin-top: 10px;
    }

    .risky-documents-list {
        max-height: 400px;
        overflow-y: auto;
        margin-top: 10px;
    }

    .document-item {
        padding: 10px;
        border-bottom: 1px solid #e9ecef;
        transition: background-color 0.2s;
    }

    .document-item:hover {
        background-color: #f8f9fa;
    }

    .document-item:last-child {
        border-bottom: none;
    }

    .risky-document {
        border-left: 4px solid #dc3545;
    }

    .warning-document {
        border-left: 4px solid #ffc107;
    }

    .info-document {
        border-left: 4px solid #0dcaf0;
    }

    .period-selector {
        display: inline-block;
        margin-left: 15px;
    }

    .form-select {
        border-radius: 4px;
        border: 1px solid #ced4da;
        padding: 0.375rem 2.25rem 0.375rem 0.75rem;
        font-size: 0.875rem;
    }

    .btn-outline-primary, .btn-outline-secondary {
        border-radius: 4px;
        font-weight: 500;
    }

    .badge {
        font-weight: 500;
        padding: 0.35em 0.65em;
    }

    .list-group-item {
        border-left: 4px solid transparent;
        padding: 0.75rem 1.25rem;
        margin-bottom: 0;
        border-radius: 0;
        border-top: 0;
        border-right: 0;
        border-bottom: 1px solid #e9ecef;
    }

    .list-group-item:last-child {
        border-bottom: 0;
    }

    .list-group-item-danger {
        border-left-color: #dc3545;
        background-color: rgba(220, 53, 69, 0.05);
    }

    .list-group-item-warning {
        border-left-color: #ffc107;
        background-color: rgba(255, 193, 7, 0.05);
    }

    .list-group-item-success {
        border-left-color: #28a745;
        background-color: rgba(40, 167, 69, 0.05);
    }

    .list-group-item-info {
        border-left-color: #0dcaf0;
        background-color: rgba(13, 202, 240, 0.05);
    }

    /* Styles pour les icônes circulaires */
    .icon-circle {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 10px;
    }

    .bg-primary-light {
        background-color: rgba(2, 117, 216, 0.1);
    }

    .bg-success-light {
        background-color: rgba(40, 167, 69, 0.1);
    }

    .bg-warning-light {
        background-color: rgba(255, 193, 7, 0.1);
    }

    .bg-danger-light {
        background-color: rgba(220, 53, 69, 0.1);
    }

    .bg-info-light {
        background-color: rgba(13, 202, 240, 0.1);
    }

    /* Styles pour les légendes et filtres */
    .legend-item {
        display: flex;
        align-items: center;
    }

    .legend-color {
        width: 16px;
        height: 3px;
        border-radius: 2px;
        margin-right: 6px;
        display: inline-block;
    }

    .gap-3 {
        gap: 1rem !important;
    }

    .form-select-sm {
        min-width: 120px;
    }

    /* Styles pour les tooltips améliorés */
    .chart-tooltip {
        background: rgba(255, 255, 255, 0.95);
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        padding: 12px;
        font-size: 13px;
        max-width: 300px;
    }

    .tooltip-header {
        font-weight: 600;
        color: #212529;
        margin-bottom: 8px;
        padding-bottom: 6px;
        border-bottom: 1px solid #e9ecef;
    }

    .tooltip-body {
        color: #495057;
    }

    .tooltip-metric {
        display: flex;
        justify-content: space-between;
        margin-bottom: 4px;
    }

    .tooltip-value {
        font-weight: 500;
        color: #0275d8;
    }

    /* Animation pour les changements de données */
    .chart-container {
        position: relative;
        transition: opacity 0.3s ease;
    }

    .chart-loading {
        opacity: 0.6;
    }

    /* Styles pour les alertes */
    .alert-container {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 1050;
        max-width: 400px;
    }

    .alert-dismissible {
        animation: slideInRight 0.3s ease;
    }

    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
</style>
{% endblock %}

{% block javascripts %}
{{ parent() }}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block body %}
<div class="container-fluid mt-4">
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="forecast-header p-4 d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 fw-bold text-dark mb-1">
                        <i class="fas fa-chart-line text-primary me-2"></i>
                        Prévisions et tendances
                    </h1>
                    <p class="text-muted mb-0">Analyse des temps de traitement et prévisions futures</p>
                </div>
                <div>
                    <a href="{{ path('app_dashboard') }}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-tachometer-alt me-1"></i> Tableau de bord
                    </a>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="row h-100">
                <div class="col-6">
                    <div class="h-100 p-3 rounded bg-white shadow-sm d-flex flex-column justify-content-center align-items-center border-top border-primary border-4">
                        <div class="icon-circle bg-primary-light mb-2">
                            <i class="fas fa-file-alt text-primary"></i>
                        </div>
                        <div class="fw-bold h4 mb-0">
                            {{ total_documents }}
                        </div>
                        <div class="text-muted small">Documents</div>
                        <div class="text-muted small mt-1">
                            <span class="badge bg-success me-1">{{ completed_documents }} terminés</span>
                            <span class="badge bg-warning">{{ active_documents }} actifs</span>
                        </div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="h-100 p-3 rounded bg-white shadow-sm d-flex flex-column justify-content-center align-items-center border-top border-warning border-4">
                        <div class="icon-circle bg-warning-light mb-2">
                            <i class="fas fa-exclamation-triangle text-warning"></i>
                        </div>
                        <div class="fw-bold h4 mb-0">
                            {{ risky_documents|length }}
                        </div>
                        <div class="text-muted small">À risque</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="forecast-card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <i class="fas fa-chart-line me-2 text-primary"></i>
                            Tendances des temps de traitement
                        </div>
                        <div class="d-flex align-items-center gap-3">
                            <div class="d-flex align-items-center">
                                <span class="me-2 small text-muted">Période :</span>
                                <select id="periodSelector" class="form-select form-select-sm">
                                    <option value="month" selected>Mensuel</option>
                                    <option value="quarter">Trimestriel</option>
                                    <option value="year">Annuel</option>
                                </select>
                            </div>
                            <div class="d-flex align-items-center">
                                <span class="me-2 small text-muted">Type :</span>
                                <select id="docTypeFilter" class="form-select form-select-sm">
                                    <option value="all" selected>Tous les types</option>
                                    <option value="ASSY">ASSY</option>
                                    <option value="MACH">MACH</option>
                                    <option value="MOLD">MOLD</option>
                                    <option value="DOC">DOC</option>
                                    <option value="PUR">PUR</option>
                                </select>
                            </div>
                            <button id="exportTrendsBtn" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-download me-1"></i>Export
                            </button>
                        </div>
                    </div>
                    <div class="row g-2">
                        <div class="col-auto">
                            <div class="d-flex align-items-center">
                                <div class="legend-item me-3">
                                    <span class="legend-color" style="background-color: rgba(67, 97, 238, 1);"></span>
                                    <span class="small">Historique</span>
                                </div>
                                <div class="legend-item me-3">
                                    <span class="legend-color" style="background-color: rgba(247, 37, 133, 1); border-style: dashed;"></span>
                                    <span class="small">Prévisions</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-auto ms-auto">
                            <div class="d-flex align-items-center gap-2">
                                <button id="zoomResetBtn" class="btn btn-sm btn-outline-secondary" disabled>
                                    <i class="fas fa-search-minus me-1"></i>Reset Zoom
                                </button>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="showDataLabels" checked>
                                    <label class="form-check-label small" for="showDataLabels">
                                        Valeurs
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="trendChart"></canvas>
                    </div>
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            Cliquez sur un point du graphique pour voir les détails de la période
                        </small>
                    </div>
                </div>
            </div>

            <div class="forecast-card mt-4">
                <div class="card-header">
                    <div>
                        <i class="fas fa-chart-line me-2 text-primary"></i>
                        Prévisions pour les 3 prochains mois
                    </div>
                    <div>
                        <span class="badge bg-light text-dark">
                            <i class="fas fa-info-circle me-1"></i> Basé sur les données historiques
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% set lastValue = processing_time_trends|last.avg_processing_time %}

                        {% for period, data in forecast %}
                            <div class="col-md-4">
                                <div class="card prediction-card
                                    {% if lastValue > 0 %}
                                        {% if data.predicted_time > lastValue * 1.1 %}
                                            up
                                        {% elseif data.predicted_time < lastValue * 0.9 %}
                                            down
                                        {% else %}
                                            stable
                                        {% endif %}
                                    {% else %}
                                        info
                                    {% endif %}
                                ">
                                    <div class="card-body p-3">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <div>
                                                <h6 class="mb-1 fw-bold">{{ data.label }}</h6>
                                                <p class="text-muted mb-0 small">Prévision</p>
                                            </div>
                                            <div class="icon-circle
                                                {% if lastValue > 0 %}
                                                    {% if data.predicted_time > lastValue * 1.1 %}
                                                        bg-danger-light
                                                    {% elseif data.predicted_time < lastValue * 0.9 %}
                                                        bg-success-light
                                                    {% else %}
                                                        bg-warning-light
                                                    {% endif %}
                                                {% else %}
                                                    bg-info-light
                                                {% endif %}
                                            ">
                                                {% if lastValue > 0 %}
                                                    {% if data.predicted_time > lastValue * 1.1 %}
                                                        <i class="fas fa-arrow-up text-danger"></i>
                                                    {% elseif data.predicted_time < lastValue * 0.9 %}
                                                        <i class="fas fa-arrow-down text-success"></i>
                                                    {% else %}
                                                        <i class="fas fa-equals text-warning"></i>
                                                    {% endif %}
                                                {% else %}
                                                    <i class="fas fa-info-circle text-info"></i>
                                                {% endif %}
                                            </div>
                                        </div>

                                        <div class="text-center py-2 bg-light rounded mb-3">
                                            <div class="stats-number">{{ data.predicted_time }}</div>
                                            <div class="stats-label">jours</div>
                                        </div>

                                        <div class="d-flex justify-content-between align-items-center">
                                            {% if lastValue > 0 %}
                                                <div>
                                                    {% if data.predicted_time > lastValue * 1.1 %}
                                                        <span class="badge bg-danger">+{{ ((data.predicted_time - lastValue) / lastValue * 100)|round(1) }}%</span>
                                                    {% elseif data.predicted_time < lastValue * 0.9 %}
                                                        <span class="badge bg-success">{{ ((data.predicted_time - lastValue) / lastValue * 100)|round(1) }}%</span>
                                                    {% else %}
                                                        <span class="badge bg-warning">{{ ((data.predicted_time - lastValue) / lastValue * 100)|round(1) }}%</span>
                                                    {% endif %}
                                                </div>
                                                <small class="text-muted">vs. actuel ({{ lastValue }} j)</small>
                                            {% else %}
                                                <span class="badge bg-info">Nouvelle tendance</span>
                                                <small class="text-muted">pas de données précédentes</small>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <div class="forecast-card mt-4">
                <div class="card-header">
                    <div>
                        <i class="fas fa-chart-pie me-2 text-info"></i>
                        Temps de traitement par type de document
                    </div>
                    <div>
                        <span class="badge bg-light text-dark">
                            <i class="fas fa-filter me-1"></i> {{ doc_type_stats|length }} types
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="fw-bold mb-3 text-dark">Répartition par nombre</h6>
                            <div class="chart-container">
                                <canvas id="docTypeCountChart"></canvas>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="fw-bold mb-3 text-dark">Répartition par temps moyen</h6>
                            <div class="chart-container">
                                <canvas id="docTypeTimeChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="forecast-card p-3 shadow-sm border rounded-3">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-exclamation-triangle me-2 mb-3 text-danger"></i>
                        Documents à risque
                    </div>
                    <div>
                        <span class="badge bg-light text-dark">
                            {{ risky_documents|length }} document(s)
                        </span>
                    </div>
                </div>
               <div class="card-body p-0">
                    <div class="risky-documents-list">
                        {% if risky_documents|length > 0 %}
                            <div class="list-group list-group-flush rounded-2 overflow-auto" style="max-height: 400px;">
                                {% for risky in risky_documents %}
                                    {# Choix de la classe selon jours en état #}
                                    {% set item_class = risky.days_in_state > 14
                                        ? 'list-group-item list-group-item-danger'
                                        : (risky.days_in_state > 7
                                            ? 'list-group-item list-group-item-warning'
                                            : 'list-group-item list-group-item-info')
                                    %}
                                    <div class="{{ item_class }}">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div class="me-3 flex-grow-1">
                                                <h6 class="mb-1 fw-bold">{{ risky.document.reference }}</h6>
                                                {# Texte tronqué automatiquement #}
                                                <p class="mb-1 small text-truncate" style="max-width: 250px;">
                                                    {{ risky.document.refTitleFra|default('Sans titre') }}
                                                </p>
                                                <div class="d-flex align-items-center mt-2">
                                                    <span class="badge bg-secondary me-2">
                                                        {{ risky.document.docType|default('N/A') }}
                                                    </span>
                                                    <span class="badge bg-light text-dark">
                                                        {{ risky.state }}
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="text-end">
                                                {# Badge pour nombre de jours #}
                                                {% set badge_color = risky.days_in_state > 14
                                                    ? 'bg-danger'
                                                    : (risky.days_in_state > 7
                                                        ? 'bg-warning'
                                                        : 'bg-info')
                                                %}
                                                <div class="badge {{ badge_color }} mb-2">
                                                    {{ risky.days_in_state }} jour{{ risky.days_in_state > 1 ? 's' }}
                                                </div>
                                                <a href="{{ path('app_forecast_document', {'id': risky.document.id}) }}"
                                                class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-chart-line me-1"></i>Prédiction
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                                <p class="mb-1 fw-semibold">Aucun document à risque</p>
                                <small class="text-muted">Tous les documents actifs sont dans les délais normaux</small>
                            </div>
                        {% endif %}
                    </div>
                </div>

            </div>

            <div class="forecast-card mt-4 p-3 shadow-sm border rounded-3">
                <div class="card-header d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <i class="fas fa-lightbulb me-2 text-warning"></i>
                        Recommandations
                    </div>
                    <div>
                        <span class="badge bg-light text-dark">
                            <i class="fas fa-brain me-1"></i> IA assistée
                        </span>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        {% if risky_documents|length > 0 %}
                            <div class="list-group-item list-group-item-danger d-flex align-items-center">
                                <div class="icon-circle bg-danger-light me-3">
                                    <i class="fas fa-exclamation-circle text-danger"></i>
                                </div>
                                <div>
                                    <strong>{{ risky_documents|length }} documents</strong> sont bloqués depuis plus de 7 jours.
                                    <div class="small text-muted mt-1">Vérifiez les documents en attente de validation.</div>
                                </div>
                            </div>
                        {% endif %}

                        {% set slowest_type = null %}
                        {% set max_time = 0 %}
                        {% for type, stats in doc_type_stats %}
                            {% if stats.avg_time > max_time %}
                                {% set slowest_type = type %}
                                {% set max_time = stats.avg_time %}
                            {% endif %}
                        {% endfor %}

                        {% if slowest_type %}
                            <div class="list-group-item list-group-item-warning d-flex align-items-center">
                                <div class="icon-circle bg-warning-light me-3">
                                    <i class="fas fa-clock text-warning"></i>
                                </div>
                                <div>
                                    Les documents de type <strong>{{ slowest_type }}</strong> prennent en moyenne <strong>{{ max_time }} jours</strong> à traiter.
                                    <div class="small text-muted mt-1">Analysez les causes de ralentissement pour ce type.</div>
                                </div>
                            </div>
                        {% endif %}

                        {% set trend = 'stable' %}
                        {% set trend_values = [] %}
                        {% for period, data in processing_time_trends|slice(-3) %}
                            {% set trend_values = trend_values|merge([data.avg_processing_time]) %}
                        {% endfor %}

                        {% if trend_values|length >= 3 %}
                            {% if trend_values[2] > trend_values[0] * 1.1 %}
                                {% set trend = 'up' %}
                            {% elseif trend_values[2] < trend_values[0] * 0.9 %}
                                {% set trend = 'down' %}
                            {% endif %}

                            {% if trend == 'up' %}
                                <div class="list-group-item list-group-item-danger d-flex align-items-center">
                                    <div class="icon-circle bg-danger-light me-3">
                                        <i class="fas fa-arrow-up text-danger"></i>
                                    </div>
                                    <div>
                                        Le temps de traitement moyen est en <strong>augmentation</strong> ces derniers mois.
                                        <div class="small text-muted mt-1">Identifiez les facteurs d'augmentation.</div>
                                    </div>
                                </div>
                            {% elseif trend == 'down' %}
                                <div class="list-group-item list-group-item-success d-flex align-items-center">
                                    <div class="icon-circle bg-success-light me-3">
                                        <i class="fas fa-arrow-down text-success"></i>
                                    </div>
                                    <div>
                                        Le temps de traitement moyen est en <strong>diminution</strong> ces derniers mois.
                                        <div class="small text-muted mt-1">Continuez avec les améliorations actuelles.</div>
                                    </div>
                                </div>
                            {% else %}
                                <div class="list-group-item list-group-item-info d-flex align-items-center">
                                    <div class="icon-circle bg-info-light me-3">
                                        <i class="fas fa-equals text-info"></i>
                                    </div>
                                    <div>
                                        Le temps de traitement moyen est <strong>stable</strong> ces derniers mois.
                                        <div class="small text-muted mt-1">Cherchez des opportunités d'amélioration.</div>
                                    </div>
                                </div>
                            {% endif %}
                        {% endif %}

                        {% if forecast|length > 0 %}
                            {% set future_trend = 'stable' %}
                            {% set first_forecast = forecast|first %}
                            {% set last_actual = processing_time_trends|last %}

                            {% if first_forecast.predicted_time > last_actual.avg_processing_time * 1.1 %}
                                {% set future_trend = 'up' %}
                            {% elseif first_forecast.predicted_time < last_actual.avg_processing_time * 0.9 %}
                                {% set future_trend = 'down' %}
                            {% endif %}

                            {% if future_trend == 'up' %}
                                <div class="list-group-item list-group-item-warning d-flex align-items-center">
                                    <div class="icon-circle bg-warning-light me-3">
                                        <i class="fas fa-chart-line text-warning"></i>
                                    </div>
                                    <div>
                                        Les prévisions indiquent une <strong>augmentation</strong> du temps de traitement dans les prochains mois.
                                        <div class="small text-muted mt-1">Planifiez des ressources supplémentaires.</div>
                                    </div>
                                </div>
                            {% elseif future_trend == 'down' %}
                                <div class="list-group-item list-group-item-success d-flex align-items-center">
                                    <div class="icon-circle bg-success-light me-3">
                                        <i class="fas fa-chart-line text-success"></i>
                                    </div>
                                    <div>
                                        Les prévisions indiquent une <strong>diminution</strong> du temps de traitement dans les prochains mois.
                                        <div class="small text-muted mt-1">Maintenez les bonnes pratiques actuelles.</div>
                                    </div>
                                </div>
                            {% else %}
                                <div class="list-group-item list-group-item-info d-flex align-items-center">
                                    <div class="icon-circle bg-info-light me-3">
                                        <i class="fas fa-chart-line text-info"></i>
                                    </div>
                                    <div>
                                        Les prévisions indiquent un temps de traitement <strong>stable</strong> dans les prochains mois.
                                        <div class="small text-muted mt-1">Maintenez le rythme actuel.</div>
                                    </div>
                                </div>
                            {% endif %}
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Section Alertes Intelligentes -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-exclamation-triangle me-2 text-warning"></i>
                        Alertes Intelligentes
                    </div>
                    <button id="refreshAlertsBtn" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-sync-alt me-1"></i>Actualiser
                    </button>
                </div>
                <div class="card-body">
                    <div id="alertsContainer">
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Chargement des alertes...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Section Analyse des Goulots -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-chart-bar me-2 text-info"></i>
                        Analyse des Goulots
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="bottleneckViewDropdown" data-bs-toggle="dropdown">
                            <i class="fas fa-filter me-1"></i>Vue
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" data-view="states">Par États</a></li>
                            <li><a class="dropdown-item" href="#" data-view="teams">Par Équipes</a></li>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    <div id="bottlenecksContainer">
                        <div class="text-center py-4">
                            <div class="spinner-border text-info" role="status">
                                <span class="visually-hidden">Chargement de l'analyse...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Conteneur pour les alertes -->
<div id="alertContainer" class="alert-container"></div>

<!-- Modal pour les détails de période -->
<div class="modal fade" id="periodDetailsModal" tabindex="-1" aria-labelledby="periodDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="periodDetailsModalLabel">
                    <i class="fas fa-chart-line me-2"></i>
                    Détails de la période
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="periodDetailsContent">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Chargement...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Données pour le graphique de tendance
        const trendData = {
            labels: [
                {% for period, data in processing_time_trends %}
                    '{{ data.label }}',
                {% endfor %}
                {% for period, data in forecast %}
                    '{{ data.label }} (prév.)',
                {% endfor %}
            ],
            datasets: [{
                label: 'Historique',
                data: [
                    {% for period, data in processing_time_trends %}
                        {{ data.avg_processing_time }},
                    {% endfor %}
                    {% for period, data in forecast %}
                        null,
                    {% endfor %}
                ],
                backgroundColor: 'rgba(67, 97, 238, 0.2)',
                borderColor: 'rgba(67, 97, 238, 1)',
                borderWidth: 3,
                tension: 0.4,
                fill: true,
                pointBackgroundColor: 'rgba(67, 97, 238, 1)',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 5,
                pointHoverRadius: 7
            },
            {
                label: 'Prévisions',
                data: [
                    {% for period, data in processing_time_trends %}
                        null,
                    {% endfor %}
                    {% for period, data in forecast %}
                        {{ data.predicted_time }},
                    {% endfor %}
                ],
                backgroundColor: 'rgba(247, 37, 133, 0.2)',
                borderColor: 'rgba(247, 37, 133, 1)',
                borderWidth: 3,
                tension: 0.4,
                fill: true,
                borderDash: [5, 5],
                pointBackgroundColor: 'rgba(247, 37, 133, 1)',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 5,
                pointHoverRadius: 7
            }]
        };

        // Configuration du graphique de tendance
        const trendConfig = {
            type: 'line',
            data: trendData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false
                },
                onClick: function(event, elements) {
                    if (elements.length > 0) {
                        const elementIndex = elements[0].index;
                        const datasetIndex = elements[0].datasetIndex;

                        // Ne traiter que les clics sur les données historiques (dataset 0)
                        if (datasetIndex === 0 && trendChart.data.datasets[0].data[elementIndex] !== null) {
                            const periodLabel = trendChart.data.labels[elementIndex];
                            // Extraire la période (enlever les suffixes comme "(prév.)")
                            const period = periodLabel.replace(' (prév.)', '');
                            showPeriodDetails(period);
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Jours',
                            font: {
                                weight: 'bold'
                            }
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)',
                            drawBorder: false
                        },
                        ticks: {
                            padding: 10
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Période',
                            font: {
                                weight: 'bold'
                            }
                        },
                        grid: {
                            display: false
                        },
                        ticks: {
                            padding: 10
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20,
                            font: {
                                size: 12
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        titleColor: '#212529',
                        bodyColor: '#212529',
                        borderColor: 'rgba(0, 0, 0, 0.1)',
                        borderWidth: 1,
                        padding: 12,
                        cornerRadius: 8,
                        displayColors: false,
                        callbacks: {
                            title: function(context) {
                                return context[0].label;
                            },
                            label: function(context) {
                                const value = context.raw;
                                const datasetLabel = context.dataset.label;
                                return `${datasetLabel}: ${value} jours`;
                            },
                            afterBody: function(context) {
                                const periodLabel = context[0].label.replace(' (prév.)', '');
                                const periodData = getCurrentPeriodData(periodLabel);

                                if (periodData) {
                                    return [
                                        `Documents traités: ${periodData.document_count || 'N/A'}`,
                                        `Temps total: ${periodData.total_time || 'N/A'} jours`,
                                        '',
                                        '💡 Cliquez pour voir les détails'
                                    ];
                                }
                                return ['💡 Cliquez pour voir les détails'];
                            }
                        }
                    }
                }
            }
        };

        // Créer le graphique de tendance
        const trendChart = new Chart(
            document.getElementById('trendChart'),
            trendConfig
        );

        // Variables globales pour les données
        let currentTrendsData = {{ processing_time_trends|json_encode|raw }};
        let currentDocTypeFilter = 'all';

        // Fonction pour récupérer les données d'une période
        function getCurrentPeriodData(periodLabel) {
            const period = periodLabel.replace(' (prév.)', '');
            for (const [key, data] of Object.entries(currentTrendsData)) {
                if (data.label === period) {
                    return data;
                }
            }
            return null;
        }

        // Données pour le graphique de répartition par type de document (nombre)
        const docTypeCountData = {
            labels: [{% for type, stats in doc_type_stats %}'{{ type }}',{% endfor %}],
            datasets: [{
                label: 'Nombre de documents',
                data: [{% for type, stats in doc_type_stats %}{{ stats.count }},{% endfor %}],
                backgroundColor: [
                    'rgba(54, 162, 235, 0.7)',
                    'rgba(255, 99, 132, 0.7)',
                    'rgba(255, 206, 86, 0.7)',
                    'rgba(75, 192, 192, 0.7)',
                    'rgba(153, 102, 255, 0.7)'
                ],
                borderWidth: 1
            }]
        };

        // Données pour le graphique de répartition par type de document (temps)
        const docTypeTimeData = {
            labels: [{% for type, stats in doc_type_stats %}'{{ type }}',{% endfor %}],
            datasets: [{
                label: 'Temps moyen (jours)',
                data: [{% for type, stats in doc_type_stats %}{{ stats.avg_time }},{% endfor %}],
                backgroundColor: [
                    'rgba(54, 162, 235, 0.7)',
                    'rgba(255, 99, 132, 0.7)',
                    'rgba(255, 206, 86, 0.7)',
                    'rgba(75, 192, 192, 0.7)',
                    'rgba(153, 102, 255, 0.7)'
                ],
                borderWidth: 1
            }]
        };

        // Configuration des graphiques en camembert
        const pieConfig = {
            type: 'pie',
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                return label + ': ' + value;
                            }
                        }
                    }
                }
            }
        };

        // Créer les graphiques de répartition par type de document
        const docTypeCountChart = new Chart(
            document.getElementById('docTypeCountChart'),
            {
                ...pieConfig,
                data: docTypeCountData
            }
        );

        const docTypeTimeChart = new Chart(
            document.getElementById('docTypeTimeChart'),
            {
                ...pieConfig,
                data: docTypeTimeData
            }
        );

        // Gestionnaire pour le filtre par type de document
        document.getElementById('docTypeFilter').addEventListener('change', function() {
            currentDocTypeFilter = this.value;
            console.log('Filtre changé vers:', currentDocTypeFilter);
            updateChartWithFilters();
        });

        // Gestionnaire pour l'affichage des valeurs
        document.getElementById('showDataLabels').addEventListener('change', function() {
            const showLabels = this.checked;
            trendChart.options.plugins.datalabels = showLabels ? {
                display: true,
                anchor: 'end',
                align: 'top',
                formatter: (value) => value ? value + 'j' : '',
                font: { size: 10, weight: 'bold' },
                color: '#495057'
            } : { display: false };
            trendChart.update();
        });

        // Gestionnaire pour le reset du zoom
        document.getElementById('zoomResetBtn').addEventListener('click', function() {
            trendChart.resetZoom();
            this.disabled = true;
        });

        // Gestionnaire pour l'export
        document.getElementById('exportTrendsBtn').addEventListener('click', function() {
            exportTrendsData();
        });

        // Fonction pour mettre à jour le graphique avec les filtres
        function updateChartWithFilters() {
            showAlert('info', 'Mise à jour des données...', 2000);

            const period = document.getElementById('periodSelector').value;
            const docType = currentDocTypeFilter;

            let url = `{{ path('app_forecast_api_trends') }}?period=${period}`;
            if (docType !== 'all') {
                url += `&doc_type=${docType}`;
            }

            console.log('URL de requête:', url);

            fetch(url)
                .then(response => {
                    console.log('Réponse reçue:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('Données reçues:', data);
                    currentTrendsData = data;
                    updateChartData(data);
                    showAlert('success', 'Données mises à jour avec succès!', 3000);
                })
                .catch(error => {
                    console.error('Erreur:', error);
                    showAlert('danger', 'Erreur lors de la mise à jour des données', 5000);
                });
        }

        // Fonction pour exporter les données
        function exportTrendsData() {
            const csvContent = generateCSV(currentTrendsData);
            downloadCSV(csvContent, 'tendances_temps_traitement.csv');
            showAlert('success', 'Export terminé!', 3000);
        }

        // Fonction pour générer le CSV
        function generateCSV(data) {
            let csv = 'Période,Temps Moyen (jours),Nombre Documents,Temps Total (jours)\n';

            // Vérifier si data est un objet ou un tableau
            if (Array.isArray(data)) {
                for (const periodData of data) {
                    csv += `"${periodData.label || 'N/A'}",${periodData.avg_processing_time || 0},${periodData.document_count || 0},${periodData.total_time || 0}\n`;
                }
            } else {
                for (const [key, periodData] of Object.entries(data)) {
                    csv += `"${periodData.label || key}",${periodData.avg_processing_time || 0},${periodData.document_count || 0},${periodData.total_time || 0}\n`;
                }
            }

            return csv;
        }

        // Fonction pour télécharger le CSV
        function downloadCSV(content, filename) {
            const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', filename);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // Fonction pour afficher les alertes
        function showAlert(type, message, duration = 5000) {
            const alertContainer = document.getElementById('alertContainer');
            const alertId = 'alert-' + Date.now();

            const alertHtml = `
                <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
                    <i class="fas fa-${getAlertIcon(type)} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `;

            alertContainer.insertAdjacentHTML('beforeend', alertHtml);

            // Auto-dismiss après la durée spécifiée
            setTimeout(() => {
                const alertElement = document.getElementById(alertId);
                if (alertElement) {
                    const bsAlert = new bootstrap.Alert(alertElement);
                    bsAlert.close();
                }
            }, duration);
        }

        // Fonction pour obtenir l'icône d'alerte
        function getAlertIcon(type) {
            const icons = {
                'success': 'check-circle',
                'danger': 'exclamation-triangle',
                'warning': 'exclamation-circle',
                'info': 'info-circle'
            };
            return icons[type] || 'info-circle';
        }

        // Fonction pour mettre à jour les données du graphique
        function updateChartData(data) {
            const labels = [];
            const historicalValues = [];
            const forecastValues = [];

            // Données historiques
            for (const [period, periodData] of Object.entries(data)) {
                labels.push(periodData.label);
                historicalValues.push(periodData.avg_processing_time);
                forecastValues.push(null);
            }

            // Ajouter les prévisions
            {% for period, data in forecast %}
                labels.push('{{ data.label }} (prév.)');
                historicalValues.push(null);
                forecastValues.push({{ data.predicted_time }});
            {% endfor %}

            trendChart.data.labels = labels;
            trendChart.data.datasets[0].data = historicalValues;
            trendChart.data.datasets[1].data = forecastValues;

            trendChart.update('active');
        }

        // Gérer le changement de période
        document.getElementById('periodSelector').addEventListener('change', function() {
            const period = this.value;

            // Afficher un indicateur de chargement
            const chartContainer = document.querySelector('.chart-container');
            const loadingOverlay = document.createElement('div');
            loadingOverlay.className = 'loading-overlay';
            loadingOverlay.innerHTML = '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">Chargement...</span></div>';
            loadingOverlay.style.position = 'absolute';
            loadingOverlay.style.top = '0';
            loadingOverlay.style.left = '0';
            loadingOverlay.style.width = '100%';
            loadingOverlay.style.height = '100%';
            loadingOverlay.style.display = 'flex';
            loadingOverlay.style.alignItems = 'center';
            loadingOverlay.style.justifyContent = 'center';
            loadingOverlay.style.backgroundColor = 'rgba(255, 255, 255, 0.7)';
            loadingOverlay.style.zIndex = '10';
            loadingOverlay.style.borderRadius = '8px';
            chartContainer.style.position = 'relative';
            chartContainer.appendChild(loadingOverlay);

            // Charger les nouvelles données
            fetch(`{{ path('app_forecast_api_trends') }}?period=${period}`)
                .then(response => response.json())
                .then(data => {
                    // Mettre à jour les labels et les données
                    const labels = [];
                    const historicalValues = [];
                    const forecastValues = [];

                    // Données historiques
                    for (const [period, periodData] of Object.entries(data)) {
                        labels.push(periodData.label);
                        historicalValues.push(periodData.avg_processing_time);
                        forecastValues.push(null);
                    }

                    // Ajouter les prévisions
                    {% for period, data in forecast %}
                        labels.push('{{ data.label }} (prév.)');
                        historicalValues.push(null);
                        forecastValues.push({{ data.predicted_time }});
                    {% endfor %}

                    trendChart.data.labels = labels;
                    trendChart.data.datasets[0].data = historicalValues;
                    trendChart.data.datasets[1].data = forecastValues;

                    // Animation de mise à jour
                    trendChart.update({
                        duration: 800,
                        easing: 'easeOutQuart'
                    });

                    // Supprimer l'indicateur de chargement
                    chartContainer.removeChild(loadingOverlay);
                })
                .catch(error => {
                    console.error('Erreur lors du chargement des données:', error);
                    // Supprimer l'indicateur de chargement en cas d'erreur
                    chartContainer.removeChild(loadingOverlay);

                    // Afficher un message d'erreur
                    const errorMessage = document.createElement('div');
                    errorMessage.className = 'alert alert-danger';
                    errorMessage.textContent = 'Erreur lors du chargement des données';
                    errorMessage.style.position = 'absolute';
                    errorMessage.style.top = '10px';
                    errorMessage.style.left = '10px';
                    errorMessage.style.right = '10px';
                    errorMessage.style.zIndex = '10';
                    chartContainer.appendChild(errorMessage);

                    // Supprimer le message d'erreur après 3 secondes
                    setTimeout(() => {
                        chartContainer.removeChild(errorMessage);
                    }, 3000);
                });
        });

        // Fonction pour afficher les détails d'une période
        function showPeriodDetails(period) {
            const modal = new bootstrap.Modal(document.getElementById('periodDetailsModal'));
            const modalTitle = document.getElementById('periodDetailsModalLabel');
            const content = document.getElementById('periodDetailsContent');

            // Mettre à jour le titre
            modalTitle.innerHTML = `<i class="fas fa-chart-line me-2"></i>Détails de la période ${period}`;

            // Afficher le spinner
            content.innerHTML = `
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                </div>
            `;

            // Afficher la modal
            modal.show();

            // Récupérer les données
            const currentPeriodType = document.getElementById('periodSelector').value;
            fetch(`{{ path('app_forecast_api_period_details') }}?period=${encodeURIComponent(period)}&period_type=${currentPeriodType}`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        content.innerHTML = `
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Erreur: ${data.error}
                            </div>
                        `;
                        return;
                    }

                    // Construire le contenu de la modal
                    let html = `
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title text-primary">${data.total_documents}</h5>
                                        <p class="card-text">Documents traités</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title text-success">${data.avg_processing_time}</h5>
                                        <p class="card-text">Temps moyen (jours)</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title text-info">${data.total_processing_time}</h5>
                                        <p class="card-text">Temps total (jours)</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title text-warning">${data.start_date} - ${data.end_date}</h5>
                                        <p class="card-text">Période</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    // Répartition par place
                    if (Object.keys(data.by_place).length > 0) {
                        html += `
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <h6 class="fw-bold mb-3">Répartition par place</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Place</th>
                                                    <th>Documents</th>
                                                    <th>Temps moyen</th>
                                                    <th>Temps total</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                        `;

                        for (const [place, stats] of Object.entries(data.by_place)) {
                            html += `
                                <tr>
                                    <td><span class="badge bg-secondary">${place}</span></td>
                                    <td>${stats.count}</td>
                                    <td>${stats.avg_time} j</td>
                                    <td>${stats.total_time} j</td>
                                </tr>
                            `;
                        }

                        html += `
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                        `;
                    }

                    // Répartition par type de document
                    if (Object.keys(data.by_doc_type).length > 0) {
                        html += `
                                <div class="col-md-6">
                                    <h6 class="fw-bold mb-3">Répartition par type</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Type</th>
                                                    <th>Documents</th>
                                                    <th>Temps moyen</th>
                                                    <th>Temps total</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                        `;

                        for (const [type, stats] of Object.entries(data.by_doc_type)) {
                            html += `
                                <tr>
                                    <td><span class="badge bg-primary">${type}</span></td>
                                    <td>${stats.count}</td>
                                    <td>${stats.avg_time} j</td>
                                    <td>${stats.total_time} j</td>
                                </tr>
                            `;
                        }

                        html += `
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        `;
                    }

                    // Liste des documents
                    if (data.documents.length > 0) {
                        html += `
                            <div class="row">
                                <div class="col-12">
                                    <h6 class="fw-bold mb-3">Documents traités (${data.documents.length})</h6>
                                    <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                                        <table class="table table-sm table-striped">
                                            <thead class="table-dark sticky-top">
                                                <tr>
                                                    <th>Référence</th>
                                                    <th>Type</th>
                                                    <th>Temps (jours)</th>
                                                    <th>Date début</th>
                                                    <th>Date fin</th>
                                                    <th>États actuels</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                        `;

                        for (const doc of data.documents) {
                            const stepsHtml = doc.current_steps.map(step =>
                                `<span class="badge bg-secondary me-1">${step}</span>`
                            ).join('');

                            html += `
                                <tr>
                                    <td><strong>${doc.reference}</strong></td>
                                    <td><span class="badge bg-primary">${doc.doc_type}</span></td>
                                    <td>${doc.processing_time}</td>
                                    <td>${doc.first_date}</td>
                                    <td>${doc.last_date}</td>
                                    <td>${stepsHtml}</td>
                                </tr>
                            `;
                        }

                        html += `
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        `;
                    }

                    content.innerHTML = html;
                })
                .catch(error => {
                    console.error('Erreur lors du chargement des détails:', error);
                    content.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Erreur lors du chargement des données
                        </div>
                    `;
                });
        }

        // Charger les alertes intelligentes
        function loadAlerts() {
            const container = document.getElementById('alertsContainer');
            container.innerHTML = `
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement des alertes...</span>
                    </div>
                </div>
            `;

            fetch('{{ path('app_forecast_api_alerts') }}')
                .then(response => response.json())
                .then(data => {
                    displayAlerts(data);
                })
                .catch(error => {
                    console.error('Erreur lors du chargement des alertes:', error);
                    container.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Erreur lors du chargement des alertes
                        </div>
                    `;
                });
        }

        // Afficher les alertes
        function displayAlerts(alertsData) {
            const container = document.getElementById('alertsContainer');
            let html = '';

            // Compter le total d'alertes
            let totalAlerts = 0;
            Object.values(alertsData).forEach(alerts => {
                if (Array.isArray(alerts)) {
                    totalAlerts += alerts.length;
                }
            });

            if (totalAlerts === 0) {
                html = `
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                        <h6 class="text-muted">Aucune alerte détectée</h6>
                        <p class="small text-muted">Tous les indicateurs sont normaux</p>
                    </div>
                `;
            } else {
                html = '<div class="list-group list-group-flush">';

                // Afficher les alertes par type
                ['bottlenecks', 'capacity_issues', 'trend_changes', 'outliers', 'performance_degradation'].forEach(type => {
                    if (alertsData[type] && alertsData[type].length > 0) {
                        alertsData[type].slice(0, 3).forEach(alert => { // Limiter à 3 par type
                            const severityClass = getSeverityClass(alert.severity);
                            const icon = getAlertTypeIcon(alert.type);

                            html += `
                                <div class="list-group-item ${severityClass} border-0">
                                    <div class="d-flex align-items-start">
                                        <div class="icon-circle bg-${getSeverityColor(alert.severity)}-light me-3">
                                            <i class="fas fa-${icon} text-${getSeverityColor(alert.severity)}"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">${alert.message}</h6>
                                            <p class="mb-1 small text-muted">${alert.recommendation}</p>
                                            <small class="text-muted">Sévérité: ${alert.severity}/5</small>
                                        </div>
                                    </div>
                                </div>
                            `;
                        });
                    }
                });

                html += '</div>';
            }

            container.innerHTML = html;
        }

        // Charger l'analyse des goulots
        function loadBottlenecks(view = 'states') {
            const container = document.getElementById('bottlenecksContainer');
            container.innerHTML = `
                <div class="text-center py-4">
                    <div class="spinner-border text-info" role="status">
                        <span class="visually-hidden">Chargement de l'analyse...</span>
                    </div>
                </div>
            `;

            let endpoint;
            if (view === 'teams') {
                endpoint = '{{ path('app_forecast_api_bottlenecks_teams') }}';
            } else {
                endpoint = '{{ path('app_forecast_api_bottlenecks_states') }}';
            }

            fetch(endpoint)
                .then(response => response.json())
                .then(data => {
                    displayBottlenecks(data, view);
                })
                .catch(error => {
                    console.error('Erreur lors du chargement des goulots:', error);
                    container.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Erreur lors du chargement de l'analyse
                        </div>
                    `;
                });
        }

        // Afficher l'analyse des goulots
        function displayBottlenecks(data, view) {
            const container = document.getElementById('bottlenecksContainer');
            let html = '';

            if (view === 'states') {
                html = '<div class="table-responsive">';
                html += `
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>État</th>
                                <th>Score</th>
                                <th>Temps Moyen</th>
                                <th>Documents</th>
                                <th>Efficacité</th>
                            </tr>
                        </thead>
                        <tbody>
                `;

                Object.entries(data).slice(0, 10).forEach(([state, stats]) => {
                    const scoreClass = getBottleneckScoreClass(stats.bottleneck_score);
                    html += `
                        <tr>
                            <td><span class="badge bg-secondary">${state}</span></td>
                            <td><span class="badge bg-${scoreClass}">${stats.bottleneck_score}</span></td>
                            <td>${stats.avg_time} j</td>
                            <td>${stats.document_count}</td>
                            <td><span class="badge bg-${getEfficiencyClass(stats.efficiency_rating)}">${stats.efficiency_rating}</span></td>
                        </tr>
                    `;
                });

                html += '</tbody></table></div>';
            } else if (view === 'teams') {
                html = '<div class="row g-3">';

                Object.entries(data).slice(0, 6).forEach(([team, stats]) => {
                    const scoreClass = getBottleneckScoreClass(stats.avg_bottleneck_score);
                    html += `
                        <div class="col-md-6">
                            <div class="card border-${scoreClass}">
                                <div class="card-body">
                                    <h6 class="card-title">${team}</h6>
                                    <p class="card-text small">
                                        Score: <span class="badge bg-${scoreClass}">${stats.avg_bottleneck_score.toFixed(1)}</span><br>
                                        Documents: ${stats.total_documents}<br>
                                        Temps moyen: ${stats.avg_time_per_document.toFixed(1)} j
                                    </p>
                                </div>
                            </div>
                        </div>
                    `;
                });

                html += '</div>';
            }

            container.innerHTML = html;
        }

        // Fonctions utilitaires
        function getSeverityClass(severity) {
            if (severity >= 4) return 'list-group-item-danger';
            if (severity >= 3) return 'list-group-item-warning';
            return 'list-group-item-info';
        }

        function getSeverityColor(severity) {
            if (severity >= 4) return 'danger';
            if (severity >= 3) return 'warning';
            return 'info';
        }

        function getAlertTypeIcon(type) {
            const icons = {
                'bottleneck': 'hourglass-half',
                'capacity_issue': 'users',
                'trend_change': 'chart-line',
                'outlier': 'exclamation-circle',
                'performance_degradation': 'arrow-down'
            };
            return icons[type] || 'info-circle';
        }

        function getBottleneckScoreClass(score) {
            if (score >= 7) return 'danger';
            if (score >= 5) return 'warning';
            if (score >= 3) return 'info';
            return 'success';
        }

        function getEfficiencyClass(rating) {
            const classes = {
                'A': 'success',
                'B': 'info',
                'C': 'warning',
                'D': 'danger',
                'F': 'dark'
            };
            return classes[rating] || 'secondary';
        }

        // Gestionnaires d'événements
        document.getElementById('refreshAlertsBtn').addEventListener('click', loadAlerts);

        // Gestionnaire pour le changement de vue des goulots
        document.querySelectorAll('#bottleneckViewDropdown + .dropdown-menu a').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const view = this.dataset.view;
                loadBottlenecks(view);

                // Mettre à jour le texte du bouton
                document.getElementById('bottleneckViewDropdown').innerHTML =
                    `<i class="fas fa-filter me-1"></i>${this.textContent}`;
            });
        });

        // Charger les données initiales
        loadAlerts();
        loadBottlenecks();
    });
</script>
{% endblock %}
