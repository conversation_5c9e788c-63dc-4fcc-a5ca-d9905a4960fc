<?php

namespace App\Service;

use App\Repository\DocumentRepository;
use App\Utils\DocumentConstants;

class AlertService
{
    private DocumentRepository $documentRepository;
    private DataAnalysisService $dataAnalysisService;

    public function __construct(
        DocumentRepository $documentRepository,
        DataAnalysisService $dataAnalysisService
    ) {
        $this->documentRepository = $documentRepository;
        $this->dataAnalysisService = $dataAnalysisService;
    }

    /**
     * Génère toutes les alertes intelligentes
     */
    public function generateAlerts(): array
    {
        try {
            return [
                'bottlenecks' => $this->detectBottlenecks(),
                'capacity_issues' => $this->detectCapacityIssues(),
                'trend_changes' => $this->detectTrendChanges(),
                'outliers' => $this->detectOutliers(),
                'performance_degradation' => $this->detectPerformanceDegradation(),
            ];
        } catch (\Exception $e) {
            // En cas d'erreur, retourner des alertes vides avec un message d'erreur
            return [
                'error' => [
                    [
                        'type' => 'system_error',
                        'severity' => 5,
                        'message' => 'Erreur lors de la génération des alertes : ' . $e->getMessage(),
                        'recommendation' => 'Vérifier les logs système et contacter l\'administrateur.'
                    ]
                ],
                'bottlenecks' => [],
                'capacity_issues' => [],
                'trend_changes' => [],
                'outliers' => [],
                'performance_degradation' => [],
            ];
        }
    }

    /**
     * Détecte les goulots d'étranglement dans le workflow
     */
    public function detectBottlenecks(): array
    {
        try {
            $bottlenecks = [];

            // Version très simplifiée avec données statiques pour test
            $bottlenecks[] = [
                'type' => 'bottleneck',
                'severity' => 4,
                'state' => 'Quality',
                'avg_time' => 12.5,
                'document_count' => 15,
                'total_time' => 187,
                'message' => "L'état 'Quality' présente un temps de traitement moyen élevé de 12.5 jours",
                'recommendation' => "Revoir les procédures qualité et former l'équipe aux nouveaux standards.",
                'affected_documents' => []
            ];

            $bottlenecks[] = [
                'type' => 'bottleneck',
                'severity' => 3,
                'state' => 'Achat',
                'avg_time' => 8.2,
                'document_count' => 22,
                'total_time' => 180,
                'message' => "L'état 'Achat' présente un temps de traitement moyen élevé de 8.2 jours",
                'recommendation' => "Optimiser les relations fournisseurs et négocier des délais plus courts.",
                'affected_documents' => []
            ];

            return $bottlenecks;
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Détecte les problèmes de capacité
     */
    public function detectCapacityIssues(): array
    {
        try {
            $issues = [];

            // Données statiques pour test
            $issues[] = [
                'type' => 'capacity_issue',
                'severity' => 3,
                'state' => 'BE_1',
                'blocked_documents' => 8,
                'avg_days_blocked' => 9.5,
                'message' => "8 documents sont bloqués dans l'état 'BE_1' depuis en moyenne 9.5 jours",
                'recommendation' => "Planifier des ressources supplémentaires pour l'état 'BE_1' cette semaine.",
                'documents' => []
            ];

            return $issues;
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Détecte les changements de tendance significatifs
     */
    public function detectTrendChanges(): array
    {
        try {
            $changes = [];

            // Données statiques pour test
            $changes[] = [
                'type' => 'trend_change',
                'severity' => 2,
                'direction' => 'increase',
                'change_percent' => 23.5,
                'recent_avg' => 7.8,
                'previous_avg' => 6.3,
                'message' => "Changement de tendance : augmentation de 23.5% du temps de traitement",
                'recommendation' => "Identifier les causes de l'augmentation des délais."
            ];

            return $changes;
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Détecte les documents outliers (temps de traitement anormaux)
     */
    public function detectOutliers(): array
    {
        try {
            $outliers = [];

            // Données statiques pour test
            $outliers[] = [
                'type' => 'outlier',
                'severity' => 4,
                'document_id' => 123,
                'reference' => 'DOC-2024-001',
                'processing_time' => 45,
                'z_score' => 3.2,
                'deviation_from_mean' => 32,
                'message' => "Document DOC-2024-001 présente un temps de traitement anormal de 45 jours",
                'recommendation' => "Analyser en détail ce document pour identifier les causes du retard exceptionnel."
            ];

            return $outliers;
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Détecte la dégradation des performances
     */
    public function detectPerformanceDegradation(): array
    {
        try {
            $degradations = [];

            // Données statiques pour test
            $degradations[] = [
                'type' => 'performance_degradation',
                'severity' => 3,
                'doc_type' => 'ASSY',
                'degradation_percent' => 18.7,
                'current_avg' => 9.2,
                'historical_avg' => 7.8,
                'message' => "Dégradation des performances pour les documents ASSY : +18.7% de temps de traitement",
                'recommendation' => "Analyser les changements récents affectant les documents ASSY et revoir la formation des équipes."
            ];

            return $degradations;
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Calcule la sévérité d'une alerte (1-5)
     */
    private function calculateSeverity(float $value, float $minThreshold, float $maxThreshold): int
    {
        if ($value <= $minThreshold) return 1;
        if ($value >= $maxThreshold) return 5;

        $ratio = ($value - $minThreshold) / ($maxThreshold - $minThreshold);
        return min(5, max(1, (int)ceil($ratio * 4) + 1));
    }

    /**
     * Recommandations pour les goulots d'étranglement
     */
    private function getBottleneckRecommendation(string $state, float $avgTime): string
    {
        $recommendations = [
            'BE_0' => "Vérifier la charge de travail de l'équipe BE. Considérer l'ajout de ressources.",
            'BE_1' => "Analyser les processus de validation BE. Automatiser si possible.",
            'Quality' => "Revoir les procédures qualité. Former l'équipe aux nouveaux standards.",
            'Achat' => "Optimiser les relations fournisseurs. Négocier des délais plus courts.",
            'default' => "Analyser les causes de ralentissement dans cet état. Revoir les processus."
        ];

        return $recommendations[$state] ?? $recommendations['default'];
    }

    /**
     * Recommandations pour les problèmes de capacité
     */
    private function getCapacityRecommendation(string $state, int $count): string
    {
        if ($count >= 10) {
            return "Urgence : Réaffecter des ressources vers l'état '{$state}' immédiatement.";
        } elseif ($count >= 5) {
            return "Planifier des ressources supplémentaires pour l'état '{$state}' cette semaine.";
        } else {
            return "Surveiller l'évolution de l'état '{$state}' et prévoir des actions si nécessaire.";
        }
    }

    /**
     * Recommandations pour les changements de tendance
     */
    private function getTrendRecommendation(float $changePercent): string
    {
        if ($changePercent > 0) {
            return "Identifier les causes de l'augmentation des délais. Revoir les processus récemment modifiés.";
        } else {
            return "Analyser les améliorations récentes pour les reproduire sur d'autres processus.";
        }
    }

    /**
     * Recommandations pour les outliers
     */
    private function getOutlierRecommendation(int $time, float $mean): string
    {
        if ($time > $mean * 2) {
            return "Analyser en détail ce document pour identifier les causes du retard exceptionnel.";
        } else {
            return "Vérifier si ce document présente des caractéristiques particulières.";
        }
    }

    /**
     * Recommandations pour la dégradation des performances
     */
    private function getPerformanceRecommendation(string $docType, float $degradation): string
    {
        return "Analyser les changements récents affectant les documents {$docType}. Revoir la formation des équipes.";
    }
}
