<?php

namespace App\Service;

use App\Repository\DocumentRepository;
use App\Utils\DocumentConstants;

class AlertService
{
    private DocumentRepository $documentRepository;
    private DataAnalysisService $dataAnalysisService;

    public function __construct(
        DocumentRepository $documentRepository,
        DataAnalysisService $dataAnalysisService
    ) {
        $this->documentRepository = $documentRepository;
        $this->dataAnalysisService = $dataAnalysisService;
    }

    /**
     * Génère toutes les alertes intelligentes
     */
    public function generateAlerts(): array
    {
        return [
            'bottlenecks' => $this->detectBottlenecks(),
            'capacity_issues' => $this->detectCapacityIssues(),
            'trend_changes' => $this->detectTrendChanges(),
            'outliers' => $this->detectOutliers(),
            'performance_degradation' => $this->detectPerformanceDegradation(),
        ];
    }

    /**
     * Détecte les goulots d'étranglement dans le workflow
     */
    public function detectBottlenecks(): array
    {
        $bottlenecks = [];
        $documents = $this->documentRepository->findAll();
        $stateStats = [];

        // Analyser le temps passé dans chaque état
        foreach ($documents as $document) {
            if (!$this->dataAnalysisService->isDocumentCompleted($document)) {
                continue;
            }

            $timestamps = $document->getRawStateTimestamps();
            if (!$timestamps) {
                continue;
            }

            foreach ($timestamps as $state => $entries) {
                if (!isset($stateStats[$state])) {
                    $stateStats[$state] = [
                        'total_time' => 0,
                        'document_count' => 0,
                        'documents' => []
                    ];
                }

                $timeInState = $document->getTotalDaysInState($state);
                if ($timeInState !== null && $timeInState > 0) {
                    $stateStats[$state]['total_time'] += $timeInState;
                    $stateStats[$state]['document_count']++;
                    $stateStats[$state]['documents'][] = [
                        'id' => $document->getId(),
                        'reference' => $document->getReference(),
                        'time_in_state' => $timeInState
                    ];
                }
            }
        }

        // Calculer les moyennes et identifier les goulots
        foreach ($stateStats as $state => $stats) {
            if ($stats['document_count'] > 0) {
                $avgTime = $stats['total_time'] / $stats['document_count'];
                
                // Considérer comme goulot si temps moyen > 5 jours
                if ($avgTime > 5) {
                    $bottlenecks[] = [
                        'type' => 'bottleneck',
                        'severity' => $this->calculateSeverity($avgTime, 5, 15),
                        'state' => $state,
                        'avg_time' => round($avgTime, 1),
                        'document_count' => $stats['document_count'],
                        'total_time' => $stats['total_time'],
                        'message' => "L'état '{$state}' présente un temps de traitement moyen élevé de " . round($avgTime, 1) . " jours",
                        'recommendation' => $this->getBottleneckRecommendation($state, $avgTime),
                        'affected_documents' => array_slice($stats['documents'], 0, 5) // Top 5
                    ];
                }
            }
        }

        // Trier par sévérité
        usort($bottlenecks, function($a, $b) {
            return $b['severity'] <=> $a['severity'];
        });

        return $bottlenecks;
    }

    /**
     * Détecte les problèmes de capacité
     */
    public function detectCapacityIssues(): array
    {
        $issues = [];
        $riskyDocuments = $this->dataAnalysisService->identifyRiskyDocuments(7);
        
        // Analyser par état
        $stateRisks = [];
        foreach ($riskyDocuments as $risky) {
            $state = $risky['state'];
            if (!isset($stateRisks[$state])) {
                $stateRisks[$state] = [];
            }
            $stateRisks[$state][] = $risky;
        }

        foreach ($stateRisks as $state => $risks) {
            $count = count($risks);
            if ($count >= 3) { // 3+ documents bloqués dans le même état
                $avgDays = array_sum(array_column($risks, 'days_in_state')) / $count;
                
                $issues[] = [
                    'type' => 'capacity_issue',
                    'severity' => $this->calculateSeverity($count, 3, 10),
                    'state' => $state,
                    'blocked_documents' => $count,
                    'avg_days_blocked' => round($avgDays, 1),
                    'message' => "{$count} documents sont bloqués dans l'état '{$state}' depuis en moyenne " . round($avgDays, 1) . " jours",
                    'recommendation' => $this->getCapacityRecommendation($state, $count),
                    'documents' => array_slice($risks, 0, 5)
                ];
            }
        }

        return $issues;
    }

    /**
     * Détecte les changements de tendance significatifs
     */
    public function detectTrendChanges(): array
    {
        $changes = [];
        $trends = $this->dataAnalysisService->analyzeProcessingTimeTrends('month', 6);
        
        if (count($trends) < 3) {
            return $changes;
        }

        $values = array_values(array_column($trends, 'avg_processing_time'));
        $recent = array_slice($values, -3); // 3 derniers mois
        $previous = array_slice($values, -6, 3); // 3 mois précédents

        if (count($recent) === 3 && count($previous) === 3) {
            $recentAvg = array_sum($recent) / 3;
            $previousAvg = array_sum($previous) / 3;
            
            $changePercent = (($recentAvg - $previousAvg) / $previousAvg) * 100;
            
            if (abs($changePercent) > 20) { // Changement > 20%
                $changes[] = [
                    'type' => 'trend_change',
                    'severity' => $this->calculateSeverity(abs($changePercent), 20, 50),
                    'direction' => $changePercent > 0 ? 'increase' : 'decrease',
                    'change_percent' => round($changePercent, 1),
                    'recent_avg' => round($recentAvg, 1),
                    'previous_avg' => round($previousAvg, 1),
                    'message' => "Changement de tendance significatif : " . 
                                ($changePercent > 0 ? "augmentation" : "diminution") . 
                                " de " . round(abs($changePercent), 1) . "% du temps de traitement moyen",
                    'recommendation' => $this->getTrendRecommendation($changePercent)
                ];
            }
        }

        return $changes;
    }

    /**
     * Détecte les documents outliers (temps de traitement anormaux)
     */
    public function detectOutliers(): array
    {
        $outliers = [];
        $documents = $this->documentRepository->findAll();
        $processingTimes = [];

        // Collecter les temps de traitement
        foreach ($documents as $document) {
            if (!$this->dataAnalysisService->isDocumentCompleted($document)) {
                continue;
            }

            $firstDate = $document->getVisaDate('visa_BE_0');
            $lastDate = $document->getVisaDate('visa_Costing');

            if ($firstDate && $lastDate) {
                if ($firstDate instanceof \DateTimeImmutable) {
                    $firstDate = \DateTime::createFromImmutable($firstDate);
                }
                if ($lastDate instanceof \DateTimeImmutable) {
                    $lastDate = \DateTime::createFromImmutable($lastDate);
                }

                $processingTime = $lastDate->diff($firstDate)->days;
                $processingTimes[] = [
                    'document' => $document,
                    'time' => $processingTime
                ];
            }
        }

        if (count($processingTimes) < 10) {
            return $outliers;
        }

        // Calculer les statistiques
        $times = array_column($processingTimes, 'time');
        $mean = array_sum($times) / count($times);
        $variance = array_sum(array_map(function($x) use ($mean) { return pow($x - $mean, 2); }, $times)) / count($times);
        $stdDev = sqrt($variance);

        // Identifier les outliers (> 2 écarts-types)
        foreach ($processingTimes as $item) {
            $zScore = abs(($item['time'] - $mean) / $stdDev);
            
            if ($zScore > 2) {
                $outliers[] = [
                    'type' => 'outlier',
                    'severity' => $this->calculateSeverity($zScore, 2, 4),
                    'document_id' => $item['document']->getId(),
                    'reference' => $item['document']->getReference(),
                    'processing_time' => $item['time'],
                    'z_score' => round($zScore, 2),
                    'deviation_from_mean' => round($item['time'] - $mean, 1),
                    'message' => "Document {$item['document']->getReference()} a un temps de traitement anormal de {$item['time']} jours (écart: " . round($zScore, 1) . "σ)",
                    'recommendation' => $this->getOutlierRecommendation($item['time'], $mean)
                ];
            }
        }

        return $outliers;
    }

    /**
     * Détecte la dégradation des performances
     */
    public function detectPerformanceDegradation(): array
    {
        $degradations = [];
        
        // Comparer les performances actuelles vs historiques par type de document
        $docTypes = ['ASSY', 'MACH', 'MOLD', 'DOC', 'PUR'];
        
        foreach ($docTypes as $docType) {
            $currentTrends = $this->dataAnalysisService->analyzeProcessingTimeTrends('month', 3, $docType);
            $historicalTrends = $this->dataAnalysisService->analyzeProcessingTimeTrends('month', 6, $docType);
            
            if (count($currentTrends) >= 2 && count($historicalTrends) >= 4) {
                $currentAvg = array_sum(array_column($currentTrends, 'avg_processing_time')) / count($currentTrends);
                $historicalAvg = array_sum(array_column(array_slice($historicalTrends, 0, -3), 'avg_processing_time')) / (count($historicalTrends) - 3);
                
                $degradationPercent = (($currentAvg - $historicalAvg) / $historicalAvg) * 100;
                
                if ($degradationPercent > 15) { // Dégradation > 15%
                    $degradations[] = [
                        'type' => 'performance_degradation',
                        'severity' => $this->calculateSeverity($degradationPercent, 15, 40),
                        'doc_type' => $docType,
                        'degradation_percent' => round($degradationPercent, 1),
                        'current_avg' => round($currentAvg, 1),
                        'historical_avg' => round($historicalAvg, 1),
                        'message' => "Dégradation des performances pour les documents {$docType} : +{$degradationPercent}% de temps de traitement",
                        'recommendation' => $this->getPerformanceRecommendation($docType, $degradationPercent)
                    ];
                }
            }
        }

        return $degradations;
    }

    /**
     * Calcule la sévérité d'une alerte (1-5)
     */
    private function calculateSeverity(float $value, float $minThreshold, float $maxThreshold): int
    {
        if ($value <= $minThreshold) return 1;
        if ($value >= $maxThreshold) return 5;
        
        $ratio = ($value - $minThreshold) / ($maxThreshold - $minThreshold);
        return min(5, max(1, (int)ceil($ratio * 4) + 1));
    }

    /**
     * Recommandations pour les goulots d'étranglement
     */
    private function getBottleneckRecommendation(string $state, float $avgTime): string
    {
        $recommendations = [
            'BE_0' => "Vérifier la charge de travail de l'équipe BE. Considérer l'ajout de ressources.",
            'BE_1' => "Analyser les processus de validation BE. Automatiser si possible.",
            'Quality' => "Revoir les procédures qualité. Former l'équipe aux nouveaux standards.",
            'Achat' => "Optimiser les relations fournisseurs. Négocier des délais plus courts.",
            'default' => "Analyser les causes de ralentissement dans cet état. Revoir les processus."
        ];
        
        return $recommendations[$state] ?? $recommendations['default'];
    }

    /**
     * Recommandations pour les problèmes de capacité
     */
    private function getCapacityRecommendation(string $state, int $count): string
    {
        if ($count >= 10) {
            return "Urgence : Réaffecter des ressources vers l'état '{$state}' immédiatement.";
        } elseif ($count >= 5) {
            return "Planifier des ressources supplémentaires pour l'état '{$state}' cette semaine.";
        } else {
            return "Surveiller l'évolution de l'état '{$state}' et prévoir des actions si nécessaire.";
        }
    }

    /**
     * Recommandations pour les changements de tendance
     */
    private function getTrendRecommendation(float $changePercent): string
    {
        if ($changePercent > 0) {
            return "Identifier les causes de l'augmentation des délais. Revoir les processus récemment modifiés.";
        } else {
            return "Analyser les améliorations récentes pour les reproduire sur d'autres processus.";
        }
    }

    /**
     * Recommandations pour les outliers
     */
    private function getOutlierRecommendation(int $time, float $mean): string
    {
        if ($time > $mean * 2) {
            return "Analyser en détail ce document pour identifier les causes du retard exceptionnel.";
        } else {
            return "Vérifier si ce document présente des caractéristiques particulières.";
        }
    }

    /**
     * Recommandations pour la dégradation des performances
     */
    private function getPerformanceRecommendation(string $docType, float $degradation): string
    {
        return "Analyser les changements récents affectant les documents {$docType}. Revoir la formation des équipes.";
    }
}
