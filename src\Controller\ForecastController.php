<?php

namespace App\Controller;

use App\Repository\DocumentRepository;
use App\Service\DataAnalysisService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/forecast')]
class ForecastController extends AbstractController
{
    private DocumentRepository $documentRepository;
    private DataAnalysisService $dataAnalysisService;

    public function __construct(
        DocumentRepository $documentRepository,
        DataAnalysisService $dataAnalysisService
    ) {
        $this->documentRepository = $documentRepository;
        $this->dataAnalysisService = $dataAnalysisService;
    }

    #[Route('/', name: 'app_forecast', methods: ['GET'])]
    public function index(): Response
    {
        // Récupérer les tendances des temps de traitement
        $processingTimeTrends = $this->dataAnalysisService->analyzeProcessingTimeTrends('month', 12);

        // Récupérer les documents à risque
        $riskyDocuments = $this->dataAnalysisService->identifyRiskyDocuments();

        // Récupérer tous les documents et les séparer en actifs/terminés
        $documents = $this->documentRepository->findAll();
        $activeDocuments = [];
        $completedDocuments = [];

        foreach ($documents as $document) {
            if ($this->dataAnalysisService->isDocumentCompleted($document)) {
                $completedDocuments[] = $document;
            } else {
                $activeDocuments[] = $document;
            }
        }

        // Calculer les statistiques par type de document (basées sur les documents terminés pour les temps)
        $docTypeStats = $this->getDocTypeStats($completedDocuments);

        // Calculer les prévisions pour les prochains mois
        $forecast = $this->calculateForecast($processingTimeTrends);

        return $this->render('forecast/index.html.twig', [
            'processing_time_trends' => $processingTimeTrends,
            'risky_documents' => $riskyDocuments,
            'doc_type_stats' => $docTypeStats,
            'forecast' => $forecast,
            'total_documents' => count($documents),
            'active_documents' => count($activeDocuments),
            'completed_documents' => count($completedDocuments),
        ]);
    }

    #[Route('/document-prediction/{id}', name: 'app_forecast_document', methods: ['GET'])]
    public function documentPrediction(int $id): Response
    {
        $document = $this->documentRepository->find($id);

        if (!$document) {
            throw $this->createNotFoundException('Document non trouvé');
        }

        // Prédire le temps de traitement pour ce document
        $prediction = $this->dataAnalysisService->predictProcessingTime($document);

        return $this->render('forecast/document_prediction.html.twig', [
            'document' => $document,
            'prediction' => $prediction,
        ]);
    }

    #[Route('/api/trends', name: 'app_forecast_api_trends', methods: ['GET'])]
    public function apiTrends(Request $request): JsonResponse
    {
        $period = $request->query->get('period', 'month');
        $limit = $request->query->getInt('limit', 12);

        $trends = $this->dataAnalysisService->analyzeProcessingTimeTrends($period, $limit);

        return new JsonResponse($trends);
    }

    #[Route('/api/period-details', name: 'app_forecast_api_period_details', methods: ['GET'])]
    public function apiPeriodDetails(Request $request): JsonResponse
    {
        $periodKey = $request->query->get('period');
        $period = $request->query->get('period_type', 'month');

        if (!$periodKey) {
            return new JsonResponse(['error' => 'Period parameter is required'], 400);
        }

        $details = $this->dataAnalysisService->getPeriodDetails($periodKey, $period);

        return new JsonResponse($details);
    }

    /**
     * Calcule les statistiques par type de document
     */
    private function getDocTypeStats(array $documents): array
    {
        $docTypes = ['ASSY', 'MACH', 'MOLD', 'DOC', 'PUR'];
        $stats = [];

        foreach ($docTypes as $docType) {
            $stats[$docType] = [
                'count' => 0,
                'avg_time' => 0,
                'total_time' => 0,
            ];
        }

        $docTypeCounts = [];
        $docTypeTotalTime = [];

        foreach ($documents as $document) {
            $docType = $document->getDocType();
            if (!$docType || !in_array($docType, $docTypes)) {
                continue;
            }

            if (!isset($docTypeCounts[$docType])) {
                $docTypeCounts[$docType] = 0;
                $docTypeTotalTime[$docType] = 0;
            }

            $docTypeCounts[$docType]++;

            // Calculer le temps total depuis la création
            $rawTimestamps = $document->getRawStateTimestamps();
            if ($rawTimestamps) {
                $totalDays = 0;
                foreach ($rawTimestamps as $state => $entries) {
                    $stateDays = $document->getTotalDaysInState($state);
                    if ($stateDays !== null) {
                        $totalDays += $stateDays;
                    }
                }
                $docTypeTotalTime[$docType] += $totalDays;
            }
        }

        // Calculer les statistiques finales
        foreach ($docTypes as $docType) {
            if (isset($docTypeCounts[$docType]) && $docTypeCounts[$docType] > 0) {
                $stats[$docType]['count'] = $docTypeCounts[$docType];
                $stats[$docType]['total_time'] = $docTypeTotalTime[$docType];
                $stats[$docType]['avg_time'] = round($docTypeTotalTime[$docType] / $docTypeCounts[$docType], 1);
            }
        }

        return $stats;
    }

    /**
     * Calcule les prévisions pour les prochains mois
     */
    private function calculateForecast(array $trends): array
    {
        $forecast = [];

        // Extraire les données historiques
        $historicalData = [];
        foreach ($trends as $period => $data) {
            $historicalData[] = $data['avg_processing_time'];
        }

        // Si nous avons moins de 3 points de données, impossible de faire une prévision fiable
        if (count($historicalData) < 3) {
            return $forecast;
        }

        // Calculer la tendance (moyenne mobile sur 3 périodes)
        $movingAverage = [];
        for ($i = 2; $i < count($historicalData); $i++) {
            $movingAverage[] = ($historicalData[$i] + $historicalData[$i-1] + $historicalData[$i-2]) / 3;
        }

        // Calculer la tendance linéaire
        $n = count($movingAverage);
        $sumX = 0;
        $sumY = 0;
        $sumXY = 0;
        $sumX2 = 0;

        for ($i = 0; $i < $n; $i++) {
            $x = $i + 1;
            $y = $movingAverage[$i];

            $sumX += $x;
            $sumY += $y;
            $sumXY += $x * $y;
            $sumX2 += $x * $x;
        }

        $slope = ($n * $sumXY - $sumX * $sumY) / ($n * $sumX2 - $sumX * $sumX);
        $intercept = ($sumY - $slope * $sumX) / $n;

        // Générer les prévisions pour les 3 prochaines périodes
        $lastPeriod = array_key_last($trends);
        $lastData = $trends[$lastPeriod];

        for ($i = 1; $i <= 3; $i++) {
            $nextX = $n + $i;
            $predictedValue = $intercept + $slope * $nextX;

            // Assurer que la valeur prédite est positive
            $predictedValue = max(0, $predictedValue);

            // Créer une étiquette pour la période suivante
            $nextPeriod = $this->getNextPeriod($lastPeriod, $i);

            $forecast[$nextPeriod] = [
                'label' => $nextPeriod,
                'predicted_time' => round($predictedValue, 1),
            ];
        }

        return $forecast;
    }

    /**
     * Calcule la période suivante en fonction de la période actuelle
     */
    private function getNextPeriod(string $currentPeriod, int $offset = 1): string
    {
        // Format attendu: MM/YYYY
        $parts = explode('/', $currentPeriod);
        if (count($parts) !== 2) {
            return 'N/A';
        }

        $month = (int)$parts[0];
        $year = (int)$parts[1];

        $month += $offset;

        while ($month > 12) {
            $month -= 12;
            $year++;
        }

        return sprintf('%02d/%d', $month, $year);
    }
}
